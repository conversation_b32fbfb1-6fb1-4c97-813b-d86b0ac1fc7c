import kotlinx.serialization.Serializable

@Serializable
data class SignalResult(
    val signalResult: List<List<Double>>,
    val faceEntityId: String,
    val gazePoints: List<GazePoint>,
    val ppgResult: PpgResult,
    val userInfo: UserInfo
)

@Serializable
data class GazePoint(
    val startTimeStamp: Long,
    val endTimestamp: Long,
    val points: List<Double>,          // 实际为空数组，用 List<Double> 占位
    val materialId: String
)

@Serializable
data class PpgResult(
    val frequencyDomain: FrequencyDomain,
    val patientId: Int,
    val rrIntervals: List<Int>,
    val timeDomain: TimeDomain,
    val totalIntervals: Int,
    val validIntervals: Int
)

@Serializable
data class FrequencyDomain(
    val hf: Double,
    val lf: Double,
    val lfHfRatio: Double,
    val stepPower: List<Double>,
    val totalPower: Double,
    val vlf: Double
)

@Serializable
data class TimeDomain(
    val meanNN: Double,
    val pnn50: Double,
    val rmssd: Double,
    val sdnn: Double,
    val sdsd: Double
)

@Serializable
data class UserInfo(
    val id: Long,
    val orgId: String,
    val patientId: String,
    val username: String,
    val nickname: String?,
    val gender: String,
    val phone: String,
    val age: Int,
    val avatar: String?,
    val clientType: String,
    val clientId: String?
)package com.airdoc.mpd

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.multidex.MultiDex
import com.airdoc.component.common.base.BaseCommonApplication
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.LogConfig
import com.airdoc.component.common.log.LogFormat
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.network.NetWorkConfigOptions
import com.airdoc.component.common.network.NetworkManager
import com.airdoc.mpd.cache.DataCacheManager
import com.jeremyliao.liveeventbus.LiveEventBus
import com.zhy.http.okhttp.OkHttpUtils
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.onAdaptListener
import okhttp3.OkHttpClient
import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.X509TrustManager

/**
 * FileName: MpdApplication
 * Author by lilin,Date on 2025/4/2 14:06
 * PS: Not easy to write code, please indicate.
 */
class MpdApplication : BaseCommonApplication() {

    companion object{
        //APP前台状态，true 在前台
        const val EVENT_APP_FOREGROUND_STATE = "APP_FOREGROUND_STATE"
        private var activityNum = 0
        @Volatile
        var isAppForeground = false
    }

    override fun onCreate() {
        super.onCreate()
        registerActivityLifecycleCallbacks(activityLifecycleCallbacks)

        initAPP()
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        //主动加载非主dex
        MultiDex.install(this)
    }

    private fun initAPP(){

        //MMKV 初始化
        MMKVManager.initMMKV(this)

        NetworkManager.initialize(this, NetWorkConfigOptions())

        //LiveEventBus
        LiveEventBus.config().enableLogger(BuildConfig.DEBUG)

        //UI 适配出丝滑
        initAutoSize()

        initOkHttpUtils()

        //LOG  日志初始化
        Logger.initialize(LogConfig().apply {
            logFormat = LogFormat.CUSTOM
        })

        // 启动数据缓存自动上传服务
        DataCacheManager.startAutoUpload(this)

    }

    //初始化屏幕适配
    private fun initAutoSize(){
        AutoSizeConfig.getInstance()
            .setCustomFragment(true)
            .setOnAdaptListener(object : onAdaptListener {
                override fun onAdaptBefore(target: Any, activity: Activity) {
                }

                override fun onAdaptAfter(target: Any, activity: Activity) {
                }

            })
            .setLog(true)
            //是否使用设备的实际尺寸做适配, 默认为 false, 如果设置为 false, 在以屏幕高度为基准进行适配时
            //AutoSize 会将屏幕总高度减去状态栏高度来做适配
            //设置为 true 则使用设备的实际屏幕高度, 不会减去状态栏高度
            //在全面屏或刘海屏幕设备中, 获取到的屏幕高度可能不包含状态栏高度, 所以在全面屏设备中不需要减去状态栏高度，所以可以 setUseDeviceSize(true)
            .setUseDeviceSize(true)

    }

    private fun initOkHttpUtils(){
        val trustAllCerts = arrayOf<X509TrustManager>(object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate?> = arrayOf()
        })

        val sslContext = SSLContext.getInstance("SSL").apply {
            init(null, trustAllCerts, SecureRandom())
        }
        val sslSocketFactory = sslContext.socketFactory
        val builder = OkHttpClient.Builder()
            .sslSocketFactory(sslSocketFactory, trustAllCerts[0])
            .hostnameVerifier(HostnameVerifier { _, _ -> true })
        val client: OkHttpClient = builder.build()
        OkHttpUtils.initClient(client)
    }

    private val activityLifecycleCallbacks = object : Application.ActivityLifecycleCallbacks{

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        }

        override fun onActivityStarted(activity: Activity) {
            activityNum++
            if (activityNum == 1){
                //应用回到前台
                isAppForeground = true
                LiveEventBus.get<Boolean>(EVENT_APP_FOREGROUND_STATE).postAcrossProcess(true)
            }
        }

        override fun onActivityResumed(activity: Activity) {
        }

        override fun onActivityPaused(activity: Activity) {
        }

        override fun onActivityStopped(activity: Activity) {
            if (activityNum > 0){
                activityNum--
            }
            if (activityNum == 0){
                //应用到了后台
                isAppForeground = false
                LiveEventBus.get<Boolean>(EVENT_APP_FOREGROUND_STATE).postAcrossProcess(false)
            }
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        }

        override fun onActivityDestroyed(activity: Activity) {
        }

    }

    override fun onTerminate() {
        super.onTerminate()
        // 停止数据缓存自动上传服务
        DataCacheManager.release()
    }

}